datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

// User Management
model User {
  id          Int      @id @default(autoincrement())
  email       String   @unique
  firstName   String
  lastName    String
  password    String
  phone       String?
  dateOfBirth DateTime?
  isActive    Boolean  @default(true)
  role        UserRole @default(CUSTOMER)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  addresses     Address[]
  orders        Order[]
  reviews       Review[]
  cartItems     CartItem[]
  wishlistItems WishlistItem[]
  paymentMethods PaymentMethod[]
  vendorProducts Product[]      @relation("VendorProducts")
  pageViews     PageView[]

  @@map("users")
}

enum UserRole {
  CUSTOMER
  ADMIN
  VENDOR
  MODERATOR
}

model Address {
  id           Int         @id @default(autoincrement())
  userId       Int
  type         AddressType @default(SHIPPING)
  firstName    String
  lastName     String
  company      String?
  addressLine1 String
  addressLine2 String?
  city         String
  state        String
  postalCode   String
  country      String
  isDefault    Boolean     @default(false)
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  // Relations
  user           User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  shippingOrders Order[]         @relation("ShippingAddress")
  billingOrders  Order[]         @relation("BillingAddress")
  shipments      Shipment[]

  @@map("addresses")
}

enum AddressType {
  SHIPPING
  BILLING
  BOTH
}

// Product Management
model Category {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  slug        String   @unique
  description String?
  image       String?
  parentId    Int?
  isActive    Boolean  @default(true)
  sortOrder   Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  parent     Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children   Category[] @relation("CategoryHierarchy")
  products   Product[]

  @@map("categories")
}

model Brand {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  slug        String   @unique
  description String?
  logo        String?
  website     String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  products Product[]

  @@map("brands")
}

model Product {
  id          Int           @id @default(autoincrement())
  name        String
  slug        String        @unique
  description String?
  shortDescription String?
  sku         String        @unique
  barcode     String?
  price       Decimal       @db.Decimal(10, 2)
  comparePrice Decimal?     @db.Decimal(10, 2)
  costPrice   Decimal?      @db.Decimal(10, 2)
  weight      Decimal?      @db.Decimal(8, 3)
  dimensions  String?       // JSON string for length, width, height
  status      ProductStatus @default(DRAFT)
  isDigital   Boolean       @default(false)
  taxable     Boolean       @default(true)
  trackQuantity Boolean     @default(true)
  categoryId  Int
  brandId     Int?
  vendorId    Int?
  metaTitle   String?
  metaDescription String?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  category      Category        @relation(fields: [categoryId], references: [id])
  brand         Brand?          @relation(fields: [brandId], references: [id])
  vendor        User?           @relation("VendorProducts", fields: [vendorId], references: [id])
  variants      ProductVariant[]
  images        ProductImage[]
  inventory     Inventory[]
  orderItems    OrderItem[]
  cartItems     CartItem[]
  wishlistItems WishlistItem[]
  reviews       Review[]
  tags          ProductTag[]
  pageViews     PageView[]

  @@map("products")
}

enum ProductStatus {
  DRAFT
  ACTIVE
  INACTIVE
  ARCHIVED
}

model ProductVariant {
  id          Int      @id @default(autoincrement())
  productId   Int
  name        String
  sku         String   @unique
  price       Decimal? @db.Decimal(10, 2)
  comparePrice Decimal? @db.Decimal(10, 2)
  costPrice   Decimal? @db.Decimal(10, 2)
  weight      Decimal? @db.Decimal(8, 3)
  barcode     String?
  position    Int      @default(0)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  product       Product           @relation(fields: [productId], references: [id], onDelete: Cascade)
  inventory     Inventory[]
  orderItems    OrderItem[]
  cartItems     CartItem[]
  wishlistItems WishlistItem[]
  variantOptions VariantOption[]

  @@map("product_variants")
}

model ProductImage {
  id        Int      @id @default(autoincrement())
  productId Int
  url       String
  altText   String?
  position  Int      @default(0)
  createdAt DateTime @default(now())

  // Relations
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

model ProductTag {
  id        Int      @id @default(autoincrement())
  productId Int
  tagId     Int
  createdAt DateTime @default(now())

  // Relations
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  tag     Tag     @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@unique([productId, tagId])
  @@map("product_tags")
}

model Tag {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  slug        String   @unique
  description String?
  createdAt   DateTime @default(now())

  // Relations
  products ProductTag[]

  @@map("tags")
}

model VariantOption {
  id        Int      @id @default(autoincrement())
  variantId Int
  name      String   // e.g., "Color", "Size"
  value     String   // e.g., "Red", "Large"
  createdAt DateTime @default(now())

  // Relations
  variant ProductVariant @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@map("variant_options")
}

// Inventory Management
model Inventory {
  id              Int      @id @default(autoincrement())
  productId       Int?
  variantId       Int?
  warehouseId     Int
  quantity        Int      @default(0)
  reservedQuantity Int     @default(0)
  reorderPoint    Int      @default(0)
  reorderQuantity Int      @default(0)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  product   Product?        @relation(fields: [productId], references: [id])
  variant   ProductVariant? @relation(fields: [variantId], references: [id])
  warehouse Warehouse       @relation(fields: [warehouseId], references: [id])

  @@unique([productId, variantId, warehouseId])
  @@map("inventory")
}

model Warehouse {
  id          Int      @id @default(autoincrement())
  name        String
  code        String   @unique
  addressLine1 String
  addressLine2 String?
  city        String
  state       String
  postalCode  String
  country     String
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  inventory Inventory[]
  shipments Shipment[]

  @@map("warehouses")
}

// Shopping Cart & Wishlist
model CartItem {
  id        Int      @id @default(autoincrement())
  userId    Int
  productId Int
  variantId Int?
  quantity  Int      @default(1)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user    User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product         @relation(fields: [productId], references: [id], onDelete: Cascade)
  variant ProductVariant? @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@unique([userId, productId, variantId])
  @@map("cart_items")
}

model WishlistItem {
  id        Int      @id @default(autoincrement())
  userId    Int
  productId Int
  variantId Int?
  createdAt DateTime @default(now())

  // Relations
  user    User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product         @relation(fields: [productId], references: [id], onDelete: Cascade)
  variant ProductVariant? @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@unique([userId, productId, variantId])
  @@map("wishlist_items")
}

// Order Management
model Order {
  id              Int         @id @default(autoincrement())
  orderNumber     String      @unique
  userId          Int
  status          OrderStatus @default(PENDING)
  subtotal        Decimal     @db.Decimal(10, 2)
  taxAmount       Decimal     @db.Decimal(10, 2)
  shippingAmount  Decimal     @db.Decimal(10, 2)
  discountAmount  Decimal     @default(0) @db.Decimal(10, 2)
  totalAmount     Decimal     @db.Decimal(10, 2)
  currency        String      @default("USD")
  shippingAddressId Int
  billingAddressId  Int
  notes           String?
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relations
  user            User        @relation(fields: [userId], references: [id])
  shippingAddress Address     @relation("ShippingAddress", fields: [shippingAddressId], references: [id])
  billingAddress  Address     @relation("BillingAddress", fields: [billingAddressId], references: [id])
  items           OrderItem[]
  payments        Payment[]
  shipments       Shipment[]
  discounts       OrderDiscount[]

  @@map("orders")
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

model OrderItem {
  id          Int      @id @default(autoincrement())
  orderId     Int
  productId   Int
  variantId   Int?
  quantity    Int
  unitPrice   Decimal  @db.Decimal(10, 2)
  totalPrice  Decimal  @db.Decimal(10, 2)
  createdAt   DateTime @default(now())

  // Relations
  order   Order           @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product         @relation(fields: [productId], references: [id])
  variant ProductVariant? @relation(fields: [variantId], references: [id])

  @@map("order_items")
}

// Payment Management
model Payment {
  id              Int           @id @default(autoincrement())
  orderId         Int
  paymentMethodId Int?
  amount          Decimal       @db.Decimal(10, 2)
  currency        String        @default("USD")
  status          PaymentStatus @default(PENDING)
  paymentGateway  String        // e.g., "stripe", "paypal"
  gatewayTransactionId String?
  gatewayResponse Json?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  order         Order          @relation(fields: [orderId], references: [id])
  paymentMethod PaymentMethod? @relation(fields: [paymentMethodId], references: [id])

  @@map("payments")
}

enum PaymentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
}

model PaymentMethod {
  id          Int               @id @default(autoincrement())
  userId      Int
  type        PaymentMethodType
  provider    String            // e.g., "visa", "mastercard", "paypal"
  last4       String?           // Last 4 digits for cards
  expiryMonth Int?
  expiryYear  Int?
  isDefault   Boolean           @default(false)
  isActive    Boolean           @default(true)
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  // Relations
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  payments Payment[]

  @@map("payment_methods")
}

enum PaymentMethodType {
  CREDIT_CARD
  DEBIT_CARD
  PAYPAL
  BANK_TRANSFER
  DIGITAL_WALLET
}

// Shipping Management
model Shipment {
  id              Int            @id @default(autoincrement())
  orderId         Int
  warehouseId     Int
  addressId       Int
  trackingNumber  String?
  carrier         String?
  shippingMethod  String?
  status          ShipmentStatus @default(PENDING)
  shippedAt       DateTime?
  deliveredAt     DateTime?
  estimatedDelivery DateTime?
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt

  // Relations
  order     Order     @relation(fields: [orderId], references: [id])
  warehouse Warehouse @relation(fields: [warehouseId], references: [id])
  address   Address   @relation(fields: [addressId], references: [id])

  @@map("shipments")
}

enum ShipmentStatus {
  PENDING
  PROCESSING
  SHIPPED
  IN_TRANSIT
  DELIVERED
  RETURNED
  CANCELLED
}

// Discount & Coupon Management
model Discount {
  id          Int          @id @default(autoincrement())
  code        String       @unique
  name        String
  description String?
  type        DiscountType
  value       Decimal      @db.Decimal(10, 2)
  minOrderAmount Decimal?  @db.Decimal(10, 2)
  maxDiscountAmount Decimal? @db.Decimal(10, 2)
  usageLimit  Int?
  usageCount  Int          @default(0)
  isActive    Boolean      @default(true)
  startsAt    DateTime?
  expiresAt   DateTime?
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  // Relations
  orderDiscounts OrderDiscount[]

  @@map("discounts")
}

enum DiscountType {
  PERCENTAGE
  FIXED_AMOUNT
  FREE_SHIPPING
}

model OrderDiscount {
  id         Int      @id @default(autoincrement())
  orderId    Int
  discountId Int
  amount     Decimal  @db.Decimal(10, 2)
  createdAt  DateTime @default(now())

  // Relations
  order    Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  discount Discount @relation(fields: [discountId], references: [id])

  @@unique([orderId, discountId])
  @@map("order_discounts")
}

// Review & Rating System
model Review {
  id        Int         @id @default(autoincrement())
  userId    Int
  productId Int
  rating    Int         // 1-5 stars
  title     String?
  comment   String?
  status    ReviewStatus @default(PENDING)
  isVerifiedPurchase Boolean @default(false)
  helpfulCount Int      @default(0)
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt

  // Relations
  user    User    @relation(fields: [userId], references: [id])
  product Product @relation(fields: [productId], references: [id])

  @@unique([userId, productId])
  @@map("reviews")
}

enum ReviewStatus {
  PENDING
  APPROVED
  REJECTED
}

// Newsletter & Marketing
model Newsletter {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("newsletters")
}

// Settings & Configuration
model Setting {
  id        Int      @id @default(autoincrement())
  key       String   @unique
  value     String
  type      String   @default("string") // string, number, boolean, json
  category  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("settings")
}

// Analytics & Tracking
model PageView {
  id        Int      @id @default(autoincrement())
  userId    Int?
  productId Int?
  page      String
  userAgent String?
  ipAddress String?
  referrer  String?
  createdAt DateTime @default(now())

  // Relations
  user    User?    @relation(fields: [userId], references: [id])
  product Product? @relation(fields: [productId], references: [id])

  @@map("page_views")
}

