{"name": "db_test", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "seed": "node src/seed.js", "generate": "node src/dataGenerator.js"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.14.0", "dependencies": {"@faker-js/faker": "^9.9.0", "@prisma/client": "^6.14.0", "faker": "^6.6.6"}, "devDependencies": {"prisma": "^6.14.0"}}