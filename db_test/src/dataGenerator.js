const { PrismaClient } = require("@prisma/client");
const { firstNames } = require("./lib/random");

const prisma = new PrismaClient();

class DatabaseSeeder {
  constructor() {
    this.users = [];
    this.categories = [];
    this.brands = [];
    this.products = [];
    this.warehouses = [];
    this.tags = [];
    this.addresses = [];
    this.orders = [];
    this.discounts = [];
  }

  generateSlug(text) {
    return text
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim("-");
  }

  // Generate Users
  async generateUsers(count = 100) {
    console.log(`Generating ${count} users...`);
    const users = [];

    for (let i = 0; i < count; i++) {
      // Randomly select from prefetched data
      const firstName =
        firstNames[Math.floor(Math.random() * firstNames.length)];
      const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
      const email = faker.internet.email({ firstName, lastName }).toLowerCase();
      const password = passwords[Math.floor(Math.random() * passwords.length)];

      const user = {
        email,
        firstName,
        lastName,
        password,
        phone:
          Math.random() > 0.3
            ? phoneNumbers[Math.floor(Math.random() * phoneNumbers.length)]
            : null,
        dateOfBirth:
          Math.random() > 0.4
            ? birthDates[Math.floor(Math.random() * birthDates.length)]
            : null,
        isActive: Math.random() > 0.1,
        role:
          i < 5
            ? "ADMIN"
            : i < 15
            ? "VENDOR"
            : i < 20
            ? "MODERATOR"
            : "CUSTOMER",
      };
      users.push(user);
    }

    const createdUsers = await prisma.user.createMany({
      data: users,
      skipDuplicates: true,
    });

    this.users = await prisma.user.findMany();
    console.log(`Created ${this.users.length} users`);
    return this.users;
  }

  // Generate Categories
  async generateCategories(count = 20) {
    console.log(`Generating ${count} categories...`);
    const categoryNames = [
      "Electronics",
      "Clothing",
      "Home & Garden",
      "Sports & Outdoors",
      "Books",
      "Health & Beauty",
      "Toys & Games",
      "Automotive",
      "Food & Beverages",
      "Jewelry",
      "Computers",
      "Smartphones",
      "Tablets",
      "Cameras",
      "Audio",
      "Gaming",
      "Men's Clothing",
      "Women's Clothing",
      "Kids' Clothing",
      "Shoes",
    ];

    const categories = [];

    // Create main categories first
    for (let i = 0; i < Math.min(count, categoryNames.length); i++) {
      const name = categoryNames[i];
      const category = {
        name,
        slug: this.generateSlug(name),
        description: faker.lorem.sentence(),
        image: faker.image.url(),
        isActive: Math.random() > 0.1,
        sortOrder: i,
      };
      categories.push(category);
    }

    await prisma.category.createMany({
      data: categories,
      skipDuplicates: true,
    });

    this.categories = await prisma.category.findMany();

    // Create subcategories
    const subcategories = [];
    for (let i = 0; i < Math.min(15, this.categories.length); i++) {
      const parent = this.randomChoice(this.categories);
      const subName = `${parent.name} - ${faker.commerce.department()}`;

      subcategories.push({
        name: subName,
        slug: this.generateSlug(subName),
        description: faker.lorem.sentence(),
        parentId: parent.id,
        isActive: Math.random() > 0.1,
        sortOrder: i,
      });
    }

    if (subcategories.length > 0) {
      await prisma.category.createMany({
        data: subcategories,
        skipDuplicates: true,
      });
    }

    this.categories = await prisma.category.findMany();
    console.log(`Created ${this.categories.length} categories`);
    return this.categories;
  }

  // Generate Brands
  async generateBrands(count = 30) {
    console.log(`Generating ${count} brands...`);
    const brands = [];

    for (let i = 0; i < count; i++) {
      const name = faker.company.name();
      const brand = {
        name,
        slug: this.generateSlug(name),
        description: faker.company.catchPhrase(),
        logo: faker.image.url(),
        website: faker.internet.url(),
        isActive: Math.random() > 0.1,
      };
      brands.push(brand);
    }

    await prisma.brand.createMany({
      data: brands,
      skipDuplicates: true,
    });

    this.brands = await prisma.brand.findMany();
    console.log(`Created ${this.brands.length} brands`);
    return this.brands;
  }

  // Generate Tags
  async generateTags(count = 50) {
    console.log(`Generating ${count} tags...`);
    const tagNames = [
      "bestseller",
      "new-arrival",
      "sale",
      "featured",
      "trending",
      "eco-friendly",
      "premium",
      "budget",
      "wireless",
      "waterproof",
      "portable",
      "durable",
      "lightweight",
      "professional",
      "gaming",
      "outdoor",
      "indoor",
      "vintage",
      "modern",
      "classic",
      "luxury",
      "affordable",
      "handmade",
      "imported",
      "organic",
      "natural",
      "synthetic",
      "recyclable",
      "energy-efficient",
    ];

    const tags = [];
    for (let i = 0; i < Math.min(count, tagNames.length); i++) {
      const name = tagNames[i];
      tags.push({
        name,
        slug: this.generateSlug(name),
        description: faker.lorem.sentence(),
      });
    }

    await prisma.tag.createMany({
      data: tags,
      skipDuplicates: true,
    });

    this.tags = await prisma.tag.findMany();
    console.log(`Created ${this.tags.length} tags`);
    return this.tags;
  }

  // Generate Warehouses
  async generateWarehouses(count = 5) {
    console.log(`Generating ${count} warehouses...`);
    const warehouses = [];

    for (let i = 0; i < count; i++) {
      const warehouse = {
        name: `${faker.location.city()} Warehouse ${i + 1}`,
        code: `WH${String(i + 1).padStart(3, "0")}`,
        addressLine1: faker.location.streetAddress(),
        addressLine2:
          Math.random() > 0.5 ? faker.location.secondaryAddress() : null,
        city: faker.location.city(),
        state: faker.location.state(),
        postalCode: faker.location.zipCode(),
        country: faker.location.country(),
        isActive: Math.random() > 0.1,
      };
      warehouses.push(warehouse);
    }

    await prisma.warehouse.createMany({
      data: warehouses,
    });

    this.warehouses = await prisma.warehouse.findMany();
    console.log(`Created ${this.warehouses.length} warehouses`);
    return this.warehouses;
  }

  // Generate Products
  async generateProducts(count = 200) {
    console.log(`Generating ${count} products...`);
    const products = [];
    const vendors = this.users.filter((u) => u.role === "VENDOR");

    for (let i = 0; i < count; i++) {
      const name = faker.commerce.productName();
      const price = this.randomFloat(10, 1000);
      const comparePrice =
        Math.random() > 0.7 ? price * this.randomFloat(1.1, 1.5) : null;
      const costPrice = price * this.randomFloat(0.4, 0.8);

      const product = {
        name,
        slug: this.generateSlug(name),
        description: faker.commerce.productDescription(),
        shortDescription: faker.lorem.sentence(),
        sku: `SKU-${faker.string.alphanumeric(8).toUpperCase()}`,
        barcode: Math.random() > 0.3 ? faker.string.numeric(12) : null,
        price,
        comparePrice,
        costPrice,
        weight: this.randomFloat(0.1, 50, 3),
        dimensions: JSON.stringify({
          length: this.randomFloat(1, 100),
          width: this.randomFloat(1, 100),
          height: this.randomFloat(1, 100),
        }),
        status: this.randomChoice(["DRAFT", "ACTIVE", "INACTIVE", "ARCHIVED"]),
        isDigital: Math.random() > 0.9,
        taxable: Math.random() > 0.1,
        trackQuantity: Math.random() > 0.1,
        categoryId: this.randomChoice(this.categories).id,
        brandId: Math.random() > 0.2 ? this.randomChoice(this.brands).id : null,
        vendorId:
          Math.random() > 0.3 && vendors.length > 0
            ? this.randomChoice(vendors).id
            : null,
        metaTitle: `${name} - Buy Online`,
        metaDescription: faker.lorem.sentence(),
      };
      products.push(product);
    }

    await prisma.product.createMany({
      data: products,
      skipDuplicates: true,
    });

    this.products = await prisma.product.findMany();
    console.log(`Created ${this.products.length} products`);
    return this.products;
  }

  // Generate Product Images
  async generateProductImages() {
    console.log("Generating product images...");
    const images = [];

    for (const product of this.products) {
      const imageCount = this.randomInt(1, 5);
      for (let i = 0; i < imageCount; i++) {
        images.push({
          productId: product.id,
          url: faker.image.url(),
          altText: `${product.name} image ${i + 1}`,
          position: i,
        });
      }
    }

    await prisma.productImage.createMany({
      data: images,
    });

    console.log(`Created ${images.length} product images`);
  }

  // Generate Product Tags
  async generateProductTags() {
    console.log("Generating product tags...");
    const productTags = [];

    for (const product of this.products) {
      const tagCount = this.randomInt(1, 5);
      const selectedTags = this.randomChoices(this.tags, tagCount);

      for (const tag of selectedTags) {
        productTags.push({
          productId: product.id,
          tagId: tag.id,
        });
      }
    }

    await prisma.productTag.createMany({
      data: productTags,
      skipDuplicates: true,
    });

    console.log(`Created ${productTags.length} product tags`);
  }

  // Generate Product Variants
  async generateProductVariants() {
    console.log("Generating product variants...");
    const variants = [];
    const variantOptions = [];

    // Only create variants for some products
    const productsWithVariants = this.products.filter(
      () => Math.random() > 0.7
    );

    for (const product of productsWithVariants) {
      const variantCount = this.randomInt(2, 5);

      for (let i = 0; i < variantCount; i++) {
        const basePrice = parseFloat(product.price);
        const priceVariation = this.randomFloat(0.8, 1.2);

        const variant = {
          productId: product.id,
          name: `${product.name} - Variant ${i + 1}`,
          sku: `${product.sku}-V${i + 1}`,
          price: basePrice * priceVariation,
          comparePrice: product.comparePrice
            ? parseFloat(product.comparePrice) * priceVariation
            : null,
          costPrice: product.costPrice
            ? parseFloat(product.costPrice) * priceVariation
            : null,
          weight: product.weight
            ? parseFloat(product.weight) * this.randomFloat(0.9, 1.1)
            : null,
          barcode: Math.random() > 0.5 ? faker.string.numeric(12) : null,
          position: i,
          isActive: Math.random() > 0.1,
        };
        variants.push(variant);
      }
    }

    if (variants.length > 0) {
      await prisma.productVariant.createMany({
        data: variants,
        skipDuplicates: true,
      });

      const createdVariants = await prisma.productVariant.findMany();

      // Generate variant options (color, size, etc.)
      const optionTypes = ["Color", "Size", "Material", "Style"];
      const colorValues = [
        "Red",
        "Blue",
        "Green",
        "Black",
        "White",
        "Yellow",
        "Purple",
      ];
      const sizeValues = ["XS", "S", "M", "L", "XL", "XXL"];
      const materialValues = [
        "Cotton",
        "Polyester",
        "Leather",
        "Metal",
        "Plastic",
      ];
      const styleValues = ["Classic", "Modern", "Vintage", "Sport", "Casual"];

      for (const variant of createdVariants) {
        const optionCount = this.randomInt(1, 3);
        const selectedOptions = this.randomChoices(optionTypes, optionCount);

        for (const optionType of selectedOptions) {
          let value;
          switch (optionType) {
            case "Color":
              value = this.randomChoice(colorValues);
              break;
            case "Size":
              value = this.randomChoice(sizeValues);
              break;
            case "Material":
              value = this.randomChoice(materialValues);
              break;
            case "Style":
              value = this.randomChoice(styleValues);
              break;
            default:
              value = faker.commerce.productAdjective();
          }

          variantOptions.push({
            variantId: variant.id,
            name: optionType,
            value: value,
          });
        }
      }

      if (variantOptions.length > 0) {
        await prisma.variantOption.createMany({
          data: variantOptions,
        });
      }

      console.log(
        `Created ${createdVariants.length} product variants with ${variantOptions.length} options`
      );
    }
  }

  // Generate Inventory
  async generateInventory() {
    console.log("Generating inventory...");
    const inventory = [];

    // Inventory for products without variants
    const productsWithoutVariants = await prisma.product.findMany({
      where: {
        variants: {
          none: {},
        },
      },
    });

    for (const product of productsWithoutVariants) {
      for (const warehouse of this.warehouses) {
        const quantity = this.randomInt(0, 1000);
        inventory.push({
          productId: product.id,
          warehouseId: warehouse.id,
          quantity,
          reservedQuantity: Math.min(quantity, this.randomInt(0, 50)),
          reorderPoint: this.randomInt(10, 100),
          reorderQuantity: this.randomInt(50, 500),
        });
      }
    }

    // Inventory for product variants
    const variants = await prisma.productVariant.findMany();
    for (const variant of variants) {
      for (const warehouse of this.warehouses) {
        const quantity = this.randomInt(0, 500);
        inventory.push({
          variantId: variant.id,
          warehouseId: warehouse.id,
          quantity,
          reservedQuantity: Math.min(quantity, this.randomInt(0, 25)),
          reorderPoint: this.randomInt(5, 50),
          reorderQuantity: this.randomInt(25, 250),
        });
      }
    }

    await prisma.inventory.createMany({
      data: inventory,
    });

    console.log(`Created ${inventory.length} inventory records`);
  }

  // Generate Addresses
  async generateAddresses() {
    console.log("Generating addresses...");
    const addresses = [];

    for (const user of this.users) {
      const addressCount = this.randomInt(1, 3);

      for (let i = 0; i < addressCount; i++) {
        const address = {
          userId: user.id,
          type: this.randomChoice(["SHIPPING", "BILLING", "BOTH"]),
          firstName: user.firstName,
          lastName: user.lastName,
          company: Math.random() > 0.7 ? faker.company.name() : null,
          addressLine1: faker.location.streetAddress(),
          addressLine2:
            Math.random() > 0.6 ? faker.location.secondaryAddress() : null,
          city: faker.location.city(),
          state: faker.location.state(),
          postalCode: faker.location.zipCode(),
          country: faker.location.country(),
          isDefault: i === 0,
        };
        addresses.push(address);
      }
    }

    await prisma.address.createMany({
      data: addresses,
    });

    this.addresses = await prisma.address.findMany();
    console.log(`Created ${this.addresses.length} addresses`);
  }

  // Generate Discounts
  async generateDiscounts(count = 20) {
    console.log(`Generating ${count} discounts...`);
    const discounts = [];

    for (let i = 0; i < count; i++) {
      const type = this.randomChoice([
        "PERCENTAGE",
        "FIXED_AMOUNT",
        "FREE_SHIPPING",
      ]);
      let value;

      switch (type) {
        case "PERCENTAGE":
          value = this.randomFloat(5, 50);
          break;
        case "FIXED_AMOUNT":
          value = this.randomFloat(10, 100);
          break;
        case "FREE_SHIPPING":
          value = 0;
          break;
      }

      const discount = {
        code: `DISCOUNT${faker.string.alphanumeric(6).toUpperCase()}`,
        name: faker.commerce.productAdjective() + " Discount",
        description: faker.lorem.sentence(),
        type,
        value,
        minOrderAmount: Math.random() > 0.5 ? this.randomFloat(50, 200) : null,
        maxDiscountAmount:
          type === "PERCENTAGE" && Math.random() > 0.5
            ? this.randomFloat(50, 500)
            : null,
        usageLimit: Math.random() > 0.3 ? this.randomInt(10, 1000) : null,
        usageCount: 0,
        isActive: Math.random() > 0.2,
        startsAt: Math.random() > 0.5 ? faker.date.recent() : null,
        expiresAt: Math.random() > 0.3 ? faker.date.future() : null,
      };
      discounts.push(discount);
    }

    await prisma.discount.createMany({
      data: discounts,
      skipDuplicates: true,
    });

    this.discounts = await prisma.discount.findMany();
    console.log(`Created ${this.discounts.length} discounts`);
  }

  // Generate Orders
  async generateOrders(count = 100) {
    console.log(`Generating ${count} orders...`);
    const orders = [];
    const orderItems = [];
    const payments = [];
    const orderDiscounts = [];

    const customers = this.users.filter((u) => u.role === "CUSTOMER");
    const availableProducts = await prisma.product.findMany({
      include: {
        variants: true,
      },
    });

    for (let i = 0; i < count; i++) {
      const customer = this.randomChoice(customers);
      const customerAddresses = this.addresses.filter(
        (a) => a.userId === customer.id
      );

      if (customerAddresses.length === 0) continue;

      const shippingAddress = this.randomChoice(customerAddresses);
      const billingAddress =
        Math.random() > 0.3
          ? shippingAddress
          : this.randomChoice(customerAddresses);

      let subtotal = 0;
      const itemCount = this.randomInt(1, 5);
      const currentOrderItems = [];

      // Generate order items
      for (let j = 0; j < itemCount; j++) {
        const product = this.randomChoice(availableProducts);
        const hasVariants = product.variants && product.variants.length > 0;
        const variant = hasVariants
          ? this.randomChoice(product.variants)
          : null;

        const quantity = this.randomInt(1, 3);
        const unitPrice = variant
          ? parseFloat(variant.price)
          : parseFloat(product.price);
        const totalPrice = unitPrice * quantity;

        subtotal += totalPrice;

        const orderItem = {
          productId: product.id,
          variantId: variant?.id || null,
          quantity,
          unitPrice,
          totalPrice,
        };
        currentOrderItems.push(orderItem);
      }

      const taxAmount = subtotal * 0.08; // 8% tax
      const shippingAmount = subtotal > 100 ? 0 : this.randomFloat(5, 25);

      // Apply discount sometimes
      let discountAmount = 0;
      let appliedDiscount = null;
      if (Math.random() > 0.7 && this.discounts.length > 0) {
        appliedDiscount = this.randomChoice(
          this.discounts.filter((d) => d.isActive)
        );
        if (appliedDiscount) {
          switch (appliedDiscount.type) {
            case "PERCENTAGE":
              discountAmount =
                subtotal * (parseFloat(appliedDiscount.value) / 100);
              if (appliedDiscount.maxDiscountAmount) {
                discountAmount = Math.min(
                  discountAmount,
                  parseFloat(appliedDiscount.maxDiscountAmount)
                );
              }
              break;
            case "FIXED_AMOUNT":
              discountAmount = Math.min(
                parseFloat(appliedDiscount.value),
                subtotal
              );
              break;
            case "FREE_SHIPPING":
              discountAmount = shippingAmount;
              break;
          }
        }
      }

      const totalAmount =
        subtotal + taxAmount + shippingAmount - discountAmount;

      const order = {
        orderNumber: `ORD-${Date.now()}-${i.toString().padStart(4, "0")}`,
        userId: customer.id,
        status: this.randomChoice([
          "PENDING",
          "CONFIRMED",
          "PROCESSING",
          "SHIPPED",
          "DELIVERED",
        ]),
        subtotal,
        taxAmount,
        shippingAmount,
        discountAmount,
        totalAmount,
        currency: "USD",
        shippingAddressId: shippingAddress.id,
        billingAddressId: billingAddress.id,
        notes: Math.random() > 0.7 ? faker.lorem.sentence() : null,
      };
      orders.push(order);

      // Store order items for later creation
      currentOrderItems.forEach((item) => {
        orderItems.push({
          ...item,
          orderIndex: i, // We'll use this to link to the created order
        });
      });

      // Store discount application
      if (appliedDiscount && discountAmount > 0) {
        orderDiscounts.push({
          orderIndex: i,
          discountId: appliedDiscount.id,
          amount: discountAmount,
        });
      }

      // Generate payment
      const payment = {
        orderIndex: i,
        amount: totalAmount,
        currency: "USD",
        status: this.randomChoice([
          "PENDING",
          "PROCESSING",
          "COMPLETED",
          "FAILED",
        ]),
        paymentGateway: this.randomChoice(["stripe", "paypal", "square"]),
        gatewayTransactionId: faker.string.alphanumeric(20),
      };
      payments.push(payment);
    }

    // Create orders
    await prisma.order.createMany({
      data: orders,
    });

    this.orders = await prisma.order.findMany({
      orderBy: { createdAt: "asc" },
    });

    // Create order items
    const orderItemsWithIds = [];
    orderItems.forEach((item, index) => {
      const orderIndex = item.orderIndex;
      const order = this.orders[orderIndex];
      if (order) {
        orderItemsWithIds.push({
          orderId: order.id,
          productId: item.productId,
          variantId: item.variantId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: item.totalPrice,
        });
      }
    });

    if (orderItemsWithIds.length > 0) {
      await prisma.orderItem.createMany({
        data: orderItemsWithIds,
      });
    }

    console.log(
      `Created ${this.orders.length} orders with ${orderItemsWithIds.length} items`
    );
  }

  // Generate Payment Methods
  async generatePaymentMethods() {
    console.log("Generating payment methods...");
    const paymentMethods = [];

    for (const user of this.users) {
      if (user.role === "CUSTOMER" && Math.random() > 0.3) {
        const methodCount = this.randomInt(1, 3);

        for (let i = 0; i < methodCount; i++) {
          const type = this.randomChoice([
            "CREDIT_CARD",
            "DEBIT_CARD",
            "PAYPAL",
            "DIGITAL_WALLET",
          ]);

          const paymentMethod = {
            userId: user.id,
            type,
            provider:
              type === "PAYPAL"
                ? "paypal"
                : type === "DIGITAL_WALLET"
                ? this.randomChoice(["apple_pay", "google_pay"])
                : this.randomChoice(["visa", "mastercard", "amex"]),
            last4: type.includes("CARD") ? faker.string.numeric(4) : null,
            expiryMonth: type.includes("CARD") ? this.randomInt(1, 12) : null,
            expiryYear: type.includes("CARD")
              ? this.randomInt(2024, 2030)
              : null,
            isDefault: i === 0,
            isActive: Math.random() > 0.1,
          };
          paymentMethods.push(paymentMethod);
        }
      }
    }

    await prisma.paymentMethod.createMany({
      data: paymentMethods,
    });

    console.log(`Created ${paymentMethods.length} payment methods`);
  }

  // Generate Reviews
  async generateReviews(count = 150) {
    console.log(`Generating ${count} reviews...`);
    const reviews = [];
    const customers = this.users.filter((u) => u.role === "CUSTOMER");

    for (let i = 0; i < count; i++) {
      const customer = this.randomChoice(customers);
      const product = this.randomChoice(this.products);

      const review = {
        userId: customer.id,
        productId: product.id,
        rating: this.randomInt(1, 5),
        title: Math.random() > 0.3 ? faker.lorem.words(3) : null,
        comment: Math.random() > 0.2 ? faker.lorem.paragraph() : null,
        status: this.randomChoice(["PENDING", "APPROVED", "REJECTED"]),
        isVerifiedPurchase: Math.random() > 0.4,
        helpfulCount: this.randomInt(0, 50),
      };
      reviews.push(review);
    }

    await prisma.review.createMany({
      data: reviews,
      skipDuplicates: true,
    });

    console.log(`Created ${reviews.length} reviews`);
  }

  // Generate Cart Items
  async generateCartItems() {
    console.log("Generating cart items...");
    const cartItems = [];
    const customers = this.users.filter((u) => u.role === "CUSTOMER");

    for (const customer of customers) {
      if (Math.random() > 0.6) {
        // Not all customers have items in cart
        const itemCount = this.randomInt(1, 5);
        const selectedProducts = this.randomChoices(this.products, itemCount);

        for (const product of selectedProducts) {
          const variants = await prisma.productVariant.findMany({
            where: { productId: product.id },
          });

          cartItems.push({
            userId: customer.id,
            productId: product.id,
            variantId:
              variants.length > 0 && Math.random() > 0.5
                ? this.randomChoice(variants).id
                : null,
            quantity: this.randomInt(1, 3),
          });
        }
      }
    }

    await prisma.cartItem.createMany({
      data: cartItems,
      skipDuplicates: true,
    });

    console.log(`Created ${cartItems.length} cart items`);
  }

  // Generate Wishlist Items
  async generateWishlistItems() {
    console.log("Generating wishlist items...");
    const wishlistItems = [];
    const customers = this.users.filter((u) => u.role === "CUSTOMER");

    for (const customer of customers) {
      if (Math.random() > 0.5) {
        // Not all customers have wishlists
        const itemCount = this.randomInt(1, 10);
        const selectedProducts = this.randomChoices(this.products, itemCount);

        for (const product of selectedProducts) {
          const variants = await prisma.productVariant.findMany({
            where: { productId: product.id },
          });

          wishlistItems.push({
            userId: customer.id,
            productId: product.id,
            variantId:
              variants.length > 0 && Math.random() > 0.7
                ? this.randomChoice(variants).id
                : null,
          });
        }
      }
    }

    await prisma.wishlistItem.createMany({
      data: wishlistItems,
      skipDuplicates: true,
    });

    console.log(`Created ${wishlistItems.length} wishlist items`);
  }

  // Generate Newsletter Subscriptions
  async generateNewsletterSubscriptions(count = 200) {
    console.log(`Generating ${count} newsletter subscriptions...`);
    const newsletters = [];

    for (let i = 0; i < count; i++) {
      newsletters.push({
        email: faker.internet.email().toLowerCase(),
        isActive: Math.random() > 0.1,
      });
    }

    await prisma.newsletter.createMany({
      data: newsletters,
      skipDuplicates: true,
    });

    console.log(`Created ${newsletters.length} newsletter subscriptions`);
  }

  // Generate Settings
  async generateSettings() {
    console.log("Generating settings...");
    const settings = [
      {
        key: "site_name",
        value: "E-Commerce Store",
        type: "string",
        category: "general",
      },
      {
        key: "site_description",
        value: "Your one-stop shop for everything",
        type: "string",
        category: "general",
      },
      { key: "currency", value: "USD", type: "string", category: "general" },
      { key: "tax_rate", value: "0.08", type: "number", category: "financial" },
      {
        key: "free_shipping_threshold",
        value: "100",
        type: "number",
        category: "shipping",
      },
      {
        key: "enable_reviews",
        value: "true",
        type: "boolean",
        category: "features",
      },
      {
        key: "enable_wishlist",
        value: "true",
        type: "boolean",
        category: "features",
      },
      { key: "max_cart_items", value: "50", type: "number", category: "cart" },
      {
        key: "email_notifications",
        value: "true",
        type: "boolean",
        category: "notifications",
      },
      {
        key: "maintenance_mode",
        value: "false",
        type: "boolean",
        category: "system",
      },
    ];

    await prisma.setting.createMany({
      data: settings,
      skipDuplicates: true,
    });

    console.log(`Created ${settings.length} settings`);
  }

  // Generate Page Views (Analytics)
  async generatePageViews(count = 1000) {
    console.log(`Generating ${count} page views...`);
    const pageViews = [];
    const pages = [
      "/home",
      "/products",
      "/categories",
      "/brands",
      "/about",
      "/contact",
      "/cart",
      "/checkout",
      "/account",
      "/orders",
      "/wishlist",
    ];

    for (let i = 0; i < count; i++) {
      const user = Math.random() > 0.3 ? this.randomChoice(this.users) : null;
      const product =
        Math.random() > 0.6 ? this.randomChoice(this.products) : null;

      pageViews.push({
        userId: user?.id || null,
        productId: product?.id || null,
        page: product ? `/products/${product.slug}` : this.randomChoice(pages),
        userAgent: faker.internet.userAgent(),
        ipAddress: faker.internet.ip(),
        referrer: Math.random() > 0.5 ? faker.internet.url() : null,
        createdAt: faker.date.recent({ days: 30 }),
      });
    }

    await prisma.pageView.createMany({
      data: pageViews,
    });

    console.log(`Created ${pageViews.length} page views`);
  }

  // Main method to run all generators
  async generateAll(options = {}) {
    const {
      users = 100,
      categories = 20,
      brands = 30,
      tags = 50,
      warehouses = 5,
      products = 200,
      orders = 100,
      discounts = 20,
      reviews = 150,
      newsletters = 200,
      pageViews = 1000,
    } = options;

    console.log("🚀 Starting database seeding...\n");

    try {
      // Clear existing data (optional)
      if (options.clearData) {
        console.log("🗑️  Clearing existing data...");
        await this.clearAllData();
      }

      // Generate base data
      await this.generateUsers(users);
      await this.generateCategories(categories);
      await this.generateBrands(brands);
      await this.generateTags(tags);
      await this.generateWarehouses(warehouses);

      // Generate products and related data
      await this.generateProducts(products);
      await this.generateProductImages();
      await this.generateProductTags();
      await this.generateProductVariants();
      await this.generateInventory();

      // Generate user-related data
      await this.generateAddresses();
      await this.generatePaymentMethods();

      // Generate orders and commerce data
      await this.generateDiscounts(discounts);
      await this.generateOrders(orders);

      // Generate user interactions
      await this.generateReviews(reviews);
      await this.generateCartItems();
      await this.generateWishlistItems();

      // Generate other data
      await this.generateNewsletterSubscriptions(newsletters);
      await this.generateSettings();
      await this.generatePageViews(pageViews);

      console.log("\n✅ Database seeding completed successfully!");

      // Print summary
      await this.printSummary();
    } catch (error) {
      console.error("❌ Error during seeding:", error);
      throw error;
    } finally {
      await prisma.$disconnect();
    }
  }

  // Clear all data
  async clearAllData() {
    const tableNames = [
      "page_views",
      "settings",
      "newsletters",
      "wishlist_items",
      "cart_items",
      "reviews",
      "order_discounts",
      "payments",
      "order_items",
      "orders",
      "discounts",
      "inventory",
      "variant_options",
      "product_variants",
      "product_tags",
      "product_images",
      "products",
      "tags",
      "brands",
      "categories",
      "warehouses",
      "payment_methods",
      "addresses",
      "users",
    ];

    for (const tableName of tableNames) {
      try {
        await prisma.$executeRawUnsafe(`DELETE FROM ${tableName}`);
        console.log(`Cleared ${tableName}`);
      } catch (error) {
        console.log(`Could not clear ${tableName}:`, error.message);
      }
    }
  }

  // Print summary of generated data
  async printSummary() {
    console.log("\n📊 Data Generation Summary:");
    console.log("================================");

    const counts = await Promise.all([
      prisma.user.count(),
      prisma.category.count(),
      prisma.brand.count(),
      prisma.tag.count(),
      prisma.warehouse.count(),
      prisma.product.count(),
      prisma.productVariant.count(),
      prisma.productImage.count(),
      prisma.inventory.count(),
      prisma.address.count(),
      prisma.order.count(),
      prisma.orderItem.count(),
      prisma.payment.count(),
      prisma.discount.count(),
      prisma.review.count(),
      prisma.cartItem.count(),
      prisma.wishlistItem.count(),
      prisma.paymentMethod.count(),
      prisma.newsletter.count(),
      prisma.setting.count(),
      prisma.pageView.count(),
    ]);

    const labels = [
      "Users",
      "Categories",
      "Brands",
      "Tags",
      "Warehouses",
      "Products",
      "Product Variants",
      "Product Images",
      "Inventory Records",
      "Addresses",
      "Orders",
      "Order Items",
      "Payments",
      "Discounts",
      "Reviews",
      "Cart Items",
      "Wishlist Items",
      "Payment Methods",
      "Newsletter Subscriptions",
      "Settings",
      "Page Views",
    ];

    labels.forEach((label, index) => {
      console.log(`${label.padEnd(20)}: ${counts[index]}`);
    });
  }
}

// Export and run if called directly
module.exports = DatabaseSeeder;

if (require.main === module) {
  const seeder = new DatabaseSeeder();

  // Parse command line arguments
  const args = process.argv.slice(2);
  const clearData = args.includes("--clear");

  seeder
    .generateAll({ clearData })
    .then(() => {
      console.log("Seeding completed!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Seeding failed:", error);
      process.exit(1);
    });
}
