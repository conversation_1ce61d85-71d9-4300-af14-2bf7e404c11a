const DatabaseSeeder = require("./dataGenerator");

async function main() {
  const seeder = new DatabaseSeeder();

  // You can customize the amounts here
  const options = {
    users: 0, // Number of users to generate
    categories: 0, // Number of categories
    brands: 0, // Number of brands
    tags: 0, // Number of tags
    warehouses: 0, // Number of warehouses
    products: 50000, // Number of products
    orders: 0, // Number of orders
    discounts: 0, // Number of discount codes
    reviews: 0, // Number of reviews
    newsletters: 0, // Number of newsletter subscriptions
    pageViews: 0, // Number of page views for analytics
    clearData: false, // Set to true to clear existing data first
  };

  try {
    await seeder.generateAll(options);
    console.log("✅ Seeding completed successfully!");
  } catch (error) {
    console.error("❌ Seeding failed:", error);
    process.exit(1);
  }
}

main();
